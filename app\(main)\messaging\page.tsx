
// Required API endpoints:
// - /api/users
// - /api/internal-messages
'use client';

import { useState, useEffect, useRef } from 'react';
// TODO: Update to use API instead of store
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type { InternalMessage, MessageStatus, User } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Search, Send, Paperclip, Check, CheckCheck, Users, X, Download, Eye, FileText, Image as ImageIcon, CheckSquare, Square, Trash2, MoreVertical } from 'lucide-react';

interface Conversation {
  threadId: number;
  participants: number[];
  lastMessageId: number;
  lastMessageText: string;
  lastMessageDate: string;
  unreadCount: number;
  groupName?: string;
}

export default function MessagingPage() {
    // State management
  const [isLoading, setIsLoading] = useState(true);

  // TODO: Add API functions here
  const { toast } = useToast();

  // Temporary: استخدام useStore للحصول على البيانات حتى يتم تحديث النظام بالكامل
  const {
    currentUser,
    users,
    internalMessages,
    sendMessage,
    updateMessage
  } = useStore();

  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConv, setSelectedConv] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<InternalMessage[]>([]);
  const [text, setText] = useState('');
  const [isNewMessageDialogOpen, setIsNewMessageDialogOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [attachment, setAttachment] = useState<File | null>(null);
  const [isGroupMembersDialogOpen, setIsGroupMembersDialogOpen] = useState(false);
  const [selectedGroupMembers, setSelectedGroupMembers] = useState<User[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [groupMessageText, setGroupMessageText] = useState('');
  const [groupAttachment, setGroupAttachment] = useState<File | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  // Temporary permissions (remove when auth system is implemented)
  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };

  const [conversationToDelete, setConversationToDelete] = useState<Conversation | null>(null);
  

  
  const messagesEnd = useRef<HTMLDivElement>(null);
  const fileInput = useRef<HTMLInputElement>(null);
  const groupFileInput = useRef<HTMLInputElement>(null);

  const isAdmin = currentUser?.permissions.messaging.viewAll;

  // دالة حفظ الرسالة بعد الإرسال (مبسطة)
  const saveMessageToDatabase = async (messageData: any) => {
    try {
      // الرسائل تُحفظ تلقائياً عبر sendMessage في store
      console.log('Message saved successfully:', messageData);
      return true;
    } catch (error) {
      console.error('Error saving message:', error);
      return false;
    }
  };













  // تحذير عند مغادرة الصفحة مع وجود نص غير مرسل
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (text.trim() || groupMessageText.trim()) {
        const message = 'لديك رسائل غير مرسلة. هل أنت متأكد من مغادرة الصفحة؟ (البيانات محفوظة في قاعدة البيانات)';
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [text, groupMessageText]);

  useEffect(() => {
    if (isGroupMembersDialogOpen && selectedConv) {
      const currentGroupMembers = users.filter(u => selectedConv.participants.includes(u.id));
      setSelectedGroupMembers(currentGroupMembers);
    }
  }, [isGroupMembersDialogOpen, selectedConv, users]);

  useEffect(() => {
    if (!currentUser) return;
  
    const convMap = new Map<number, Conversation>();
  
    const relevantMessages = internalMessages.filter(m => {
      // User is either sender or a recipient
      return m.senderId === currentUser.id || 
             (m.recipientIds && m.recipientIds.includes(currentUser.id)) ||
             m.recipientId === currentUser.id ||
             (m.recipientId === 0 && m.recipientIds?.includes(currentUser.id)); // System messages for groups
    });

    relevantMessages.forEach(m => {
        let otherParticipants: number[];
        if (m.recipientIds && m.recipientIds.length > 1) {
            otherParticipants = [m.senderId, ...m.recipientIds];
        } else if (m.recipientId && m.recipientId !== 0) {
            otherParticipants = [m.senderId, m.recipientId];
        } else {
            otherParticipants = [m.senderId];
        }
        
        const uniqueParticipants = [...new Set(otherParticipants.filter(p => p !== 0 && p !== undefined))];
      
        let existing = convMap.get(m.threadId);
  
        if (!existing) {
          existing = {
            threadId: m.threadId,
            participants: uniqueParticipants,
            lastMessageId: -1,
            lastMessageText: '',
            lastMessageDate: new Date(0).toISOString(),
            unreadCount: 0,
            groupName: uniqueParticipants.length > 2 
              ? `مجموعة (${uniqueParticipants.length})` 
              : undefined,
          };
        }
  
        if (new Date(m.sentDate) > new Date(existing.lastMessageDate)) {
            existing.lastMessageText = m.text;
            existing.lastMessageDate = m.sentDate;
            existing.lastMessageId = m.id;
        }
  
        const isUnread = (m.recipientId === currentUser.id || (m.recipientIds && m.recipientIds.includes(currentUser.id))) && !m.isRead;
        if (isUnread) {
          if (!convMap.has(m.threadId)) {
            existing.unreadCount = 1;
          } else if (convMap.get(m.threadId)!.lastMessageId <= m.id) {
             convMap.get(m.threadId)!.unreadCount++;
          }
        }
        
        // Update participants list if new ones are found
        existing.participants = uniqueParticipants;
  
        convMap.set(m.threadId, existing);
    });
  
    const convs = Array.from(convMap.values()).sort((a,b) => new Date(b.lastMessageDate).getTime() - new Date(a.lastMessageDate).getTime());
    setConversations(convs);
  
  }, [internalMessages, currentUser, isAdmin]);

  const selectConversation = (conv: Conversation) => {
    setSelectedConv(conv);
  };

  useEffect(() => { messagesEnd.current?.scrollIntoView({ behavior: 'smooth' }); }, [messages]);

  useEffect(() => {
    if (selectedConv) {
      const msgs = internalMessages.filter(m => m.threadId === selectedConv.threadId)
        .sort((a, b) => new Date(a.sentDate).getTime() - new Date(b.sentDate).getTime());
      
      setMessages(msgs);

      msgs.forEach(m => {
        if ((m.recipientId === currentUser?.id || m.recipientIds?.includes(currentUser?.id ?? -1)) && !m.isRead) {
          updateMessage(m.id, { isRead: true, status: 'مقروءة' as MessageStatus });
        }
      });
    } else {
      setMessages([]);
    }
  }, [selectedConv, internalMessages, currentUser?.id, updateMessage]);

  const handleSend = async () => {
    if ((!text.trim() && !attachment) || !currentUser || !selectedConv) return;

    // حفظ النص مؤقتاً للاستعادة في حالة الفشل
    const tempText = text;
    const tempAttachment = attachment;

    let attachmentUrl: string | undefined;
    let attachmentFileName: string | undefined;
    let attachmentContent: string | undefined;
    let attachmentType: string | undefined;

    try {
      if (attachment) {
        try {
          // رفع الملف إلى الخادم
          const formData = new FormData();
          formData.append('file', attachment);

          const response = await fetch('/api/attachments', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error('فشل في رفع الملف');
          }

          const result = await response.json();
          attachmentUrl = result.url;
          attachmentFileName = result.fileName;
          attachmentType = attachment.type;

          // للصور الصغيرة، احتفظ بـ Base64 للعرض السريع
          if (isImageFile(attachment.name) && attachment.size <= 1024 * 1024) {
            const reader = new FileReader();
            reader.readAsDataURL(attachment);
            await new Promise<void>((resolve, reject) => {
              reader.onload = () => {
                attachmentContent = reader.result as string;
                resolve();
              };
              reader.onerror = reject;
            });
          }

        } catch (error) {
          console.error('خطأ في رفع الملف:', error);
          toast({
            variant: 'destructive',
            title: 'خطأ في رفع الملف',
            description: 'حدث خطأ أثناء رفع الملف. يرجى المحاولة مرة أخرى.',
          });
          return;
        }
      }

      if (selectedConv.participants.length > 2) { // Group chat
          await sendMessage({
              recipientIds: selectedConv.participants.filter(pid => pid !== currentUser.id),
              text,
              attachmentContent,
              attachmentType,
              attachmentName: attachment?.name,
              attachmentUrl,
              attachmentFileName,
              attachmentSize: attachment?.size,
              parentMessageId: selectedConv.threadId,
              recipientId: 0,
              recipientName: 'Group'
          });
      } else { // Direct message
          const recipientId = selectedConv.participants.find((pid: number) => pid !== currentUser.id)!;
          const recipient = users.find(u => u.id === recipientId);

          await sendMessage({
              recipientId: recipientId,
              recipientName: recipient?.name || '',
              text,
              attachmentContent,
              attachmentType,
              attachmentName: attachment?.name,
              attachmentUrl,
              attachmentFileName,
              attachmentSize: attachment?.size,
              parentMessageId: selectedConv.threadId,
          });
      }

      // إجبار تحديث المحادثة فوراً
      forceUpdateConversation(selectedConv.threadId, text || (attachment ? `📎 ${attachment.name}` : ''));

      // إضافة الرسالة مباشرة إلى قائمة الرسائل للعرض الفوري
      const newMessage: InternalMessage = {
        id: Date.now(),
        threadId: selectedConv.threadId,
        senderId: currentUser.id,
        senderName: currentUser.name,
        recipientId: selectedConv.participants.length > 2 ? 0 : selectedConv.participants.find(p => p !== currentUser.id)!,
        recipientName: selectedConv.participants.length > 2 ? 'Group' : users.find(u => u.id === selectedConv.participants.find(p => p !== currentUser.id))?.name || '',
        text,
        attachmentName: attachment?.name,
        attachmentContent,
        attachmentType,
        attachmentUrl,
        attachmentFileName,
        attachmentSize: attachment?.size,
        sentDate: new Date().toISOString(),
        status: 'مرسلة',
        isRead: false,
        recipientIds: selectedConv.participants.length > 2 ? selectedConv.participants.filter(p => p !== currentUser.id) : undefined,
      };

      setMessages(prev => [...prev, newMessage]);

      // مسح النص والمرفق بعد الإرسال الناجح
      setText('');
      setAttachment(null);
      if(fileInput.current) fileInput.current.value = '';
      
      // حفظ الرسالة بعد الإرسال الناجح
      try {
        await saveMessageToDatabase({ text, recipientId: selectedConv?.threadId });
      } catch (error) {
        console.error('Error saving message after send:', error);
      }

      toast({
        title: 'تم إرسال الرسالة',
        description: 'تم إرسال الرسالة بنجاح',
        variant: 'default'
      });

    } catch (error) {
      // استعادة النص والمرفق في حالة الفشل
      setText(tempText);
      setAttachment(tempAttachment);
      
      console.error('خطأ في إرسال الرسالة:', error);
      toast({
        variant: 'destructive',
        title: 'فشل في الإرسال',
        description: 'حدث خطأ أثناء إرسال الرسالة. تم الاحتفاظ بالمسودة.',
      });
    }
  };
  
  const handleGroupSend = async () => {
    if ((!groupMessageText.trim() && !groupAttachment) || !currentUser || selectedUsers.length === 0) return;

    let attachmentUrl: string | undefined;
    let attachmentFileName: string | undefined;
    let attachmentContent: string | undefined;
    let attachmentType: string | undefined;

    if (groupAttachment) {
      try {
        // رفع الملف إلى الخادم
        const formData = new FormData();
        formData.append('file', groupAttachment);

        const response = await fetch('/api/attachments', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error('فشل في رفع الملف');
        }

        const result = await response.json();
        attachmentUrl = result.url;
        attachmentFileName = result.fileName;
        attachmentType = groupAttachment.type;

        // للصور الصغيرة، احتفظ بـ Base64 للعرض السريع
        if (isImageFile(groupAttachment.name) && groupAttachment.size <= 1024 * 1024) {
          const reader = new FileReader();
          reader.readAsDataURL(groupAttachment);
          await new Promise<void>((resolve, reject) => {
            reader.onload = () => {
              attachmentContent = reader.result as string;
              resolve();
            };
            reader.onerror = reject;
          });
        }

      } catch (error) {
        console.error('خطأ في رفع الملف:', error);
        toast({
          variant: 'destructive',
          title: 'خطأ في رفع الملف',
          description: 'حدث خطأ أثناء رفع الملف. يرجى المحاولة مرة أخرى.',
        });
        return;
      }
    }

    // إرسال رسائل منفصلة لكل موظف بدلاً من مجموعة
    for (const user of selectedUsers) {
      await sendMessage({
        recipientId: user.id,
        recipientName: user.name,
        text: groupMessageText,
        attachmentContent,
        attachmentType,
        attachmentName: groupAttachment?.name,
        attachmentUrl,
        attachmentFileName,
        attachmentSize: groupAttachment?.size,
      });
    }

    setGroupMessageText('');
    setGroupAttachment(null);
    setSelectedUsers([]);
    setSelectAll(false);
    setIsNewMessageDialogOpen(false);

    toast({
      title: 'تم الإرسال',
      description: `تم إرسال الرسالة إلى ${selectedUsers.length} موظف`,
    });
  }

  const handleSelectUser = (user: User) => {
    setSelectedUsers(prev =>
      prev.some(su => su.id === user.id)
        ? prev.filter(su => su.id !== user.id)
        : [...prev, user]
    );
  };

  const handleSelectAll = () => {
    const availableUsers = users.filter(user => user.id !== currentUser?.id);
    if (selectAll) {
      setSelectedUsers([]);
      setSelectAll(false);
    } else {
      setSelectedUsers(availableUsers);
      setSelectAll(true);
    }
  };

  const handleDirectMessage = (user: User) => {
    if (!currentUser) return;

    // البحث عن محادثة موجودة
    const existingConv = conversations.find(c =>
      c.participants.length === 2 &&
      c.participants.includes(currentUser.id) &&
      c.participants.includes(user.id)
    );

    if (existingConv) {
      selectConversation(existingConv);
      setIsNewMessageDialogOpen(false);
    } else {
      // إنشاء محادثة جديدة مباشرة
      const newThreadId = Date.now();
      const newConv: Conversation = {
        threadId: newThreadId,
        participants: [currentUser.id, user.id],
        lastMessageId: -1,
        lastMessageText: '',
        lastMessageDate: new Date().toISOString(),
        unreadCount: 0,
      };

      // إضافة المحادثة الجديدة وتحديد الحالة
      setConversations(prev => [newConv, ...prev]);
      setSelectedConv(newConv);
      setMessages([]); // تأكد من أن قائمة الرسائل فارغة للمحادثة الجديدة
      setIsNewMessageDialogOpen(false);
    }
  };

  const handleStartConversation = () => {
    if (selectedUsers.length === 0 || !currentUser) return;

    const participantIds = [currentUser.id, ...selectedUsers.map((u) => u.id)];
    const isGroup = participantIds.length > 2;

    let existingConv: Conversation | undefined;

    if (!isGroup) {
      existingConv = conversations.find(c =>
        c.participants.length === 2 &&
        c.participants.includes(participantIds[0]) &&
        c.participants.includes(participantIds[1])
      );
    } else {
      // Simplified logic for finding existing groups: find a group with the exact same participants
      existingConv = conversations.find(c =>
          c.participants.length === participantIds.length &&
          participantIds.every(id => c.participants.includes(id))
      );
    }

    if (existingConv) {
      selectConversation(existingConv);
    } else {
      const newThreadId = Date.now();
      const newConv: Conversation = {
        threadId: newThreadId,
        participants: participantIds,
        lastMessageId: -1,
        lastMessageText: '',
        lastMessageDate: new Date().toISOString(),
        unreadCount: 0,
        groupName: isGroup ? `مجموعة (${participantIds.length})` : undefined,
      };

      // إضافة المحادثة الجديدة وتحديد الحالة
      setConversations(prev => [newConv, ...prev]);
      setSelectedConv(newConv);
      setMessages([]); // تأكد من أن قائمة الرسائل فارغة للمحادثة الجديدة
    }

    setIsNewMessageDialogOpen(false);
    setSelectedUsers([]);
    setSelectAll(false);
  };

  const handleSelectGroupMember = (user: User) => {
    setSelectedGroupMembers(prev =>
      prev.some(su => su.id === user.id)
        ? prev.filter(su => su.id !== user.id)
        : [...prev, user]
    );
  };

  const handleUpdateGroupMembers = () => {
    if (!selectedConv || !currentUser) return;

    const updatedParticipants = selectedGroupMembers.map(u => u.id);

    if (selectedConv.participants.includes(currentUser.id) && !updatedParticipants.includes(currentUser.id)) {
      updatedParticipants.push(currentUser.id);
    }

    const updatedConv: Conversation = {
      ...selectedConv,
      participants: updatedParticipants,
      groupName: updatedParticipants.length > 2
        ? `مجموعة (${updatedParticipants.length})`
        : undefined,
    };

    setConversations(prev => prev.map(c => c.threadId === updatedConv.threadId ? updatedConv : c));
    setSelectedConv(updatedConv); 

    toast({
      title: "تم تحديث أعضاء المجموعة",
      description: "تم حفظ التغييرات بنجاح.",
    });

    setIsGroupMembersDialogOpen(false);
  };
  
  const getConversationName = (conv: Conversation) => {
    if (conv.groupName) return conv.groupName;
    if (!currentUser) return '';
    const otherId = conv.participants.find(p => p !== currentUser.id);
    const otherUser = users.find(u => u.id === otherId);
    return otherUser?.name || 'مستخدم محذوف';
  };

  const downloadFile = (content: string, filename: string) => {
    const link = document.createElement('a');
    link.href = content;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const saveImageToFolder = async (content: string, filename: string) => {
    try {
      // استخدام File System Access API إذا كان متاحاً
      if ('showSaveFilePicker' in window) {
        const fileHandle = await (window as any).showSaveFilePicker({
          suggestedName: filename,
          types: [{
            description: 'Images',
            accept: {
              'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp']
            }
          }]
        });

        const response = await fetch(content);
        const blob = await response.blob();
        const writable = await fileHandle.createWritable();
        await writable.write(blob);
        await writable.close();

        toast({
          title: 'تم الحفظ',
          description: 'تم حفظ الصورة بنجاح',
        });
      } else {
        // fallback للمتصفحات القديمة
        downloadFile(content, filename);
      }
    } catch (error) {
      console.error('Error saving file:', error);
      // fallback في حالة الخطأ
      downloadFile(content, filename);
    }
  };

  const openFile = (content: string, filename: string) => {
    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>${filename}</title>
            <style>
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              iframe) { width: 100%; height: calc(100vh - 40px); border: none; }
              .download-btn {
                position: fixed;
                top: 10px;
                right: 10px;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
              }
            </style>
          </head>
          <body>
            <button class="download-btn" onclick="window.open('${content}', '_blank')">تحميل الملف</button>
            <iframe src="${content}"></iframe>
          </body>
        </html>
      `);
    }
  };

  const isImageFile = (filename: string) => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
    return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
  };

  const isPdfFile = (filename: string) => {
    return filename.toLowerCase().endsWith('.pdf');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const downloadAttachment = (url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDeleteConversation = (conv: Conversation) => {
    setConversationToDelete(conv);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteConversation = () => {
    if (!conversationToDelete) return;

    // حذف جميع الرسائل المتعلقة بهذه المحادثة
    conversationToDelete.participants.forEach(participantId => {
      const messagesToDelete = internalMessages.filter(m =>
        m.threadId === conversationToDelete.threadId
      );

      messagesToDelete.forEach(message => {
        // هنا يمكن إضافة منطق حذف الرسائل من المتجر
        // لكن حالياً سنقوم بإزالة المحادثة من القائمة فقط
      });
    });

    // إزالة المحادثة من القائمة
    setConversations(prev => prev.filter(c => c.threadId !== conversationToDelete.threadId));

    // إذا كانت المحادثة المحذوفة هي المحددة حالياً، قم بإلغاء التحديد
    if (selectedConv?.threadId === conversationToDelete.threadId) {
      setSelectedConv(null);
      setMessages([]);
    }

    toast({
      title: 'تم حذف المحادثة',
      description: 'تم حذف المحادثة وجميع رسائلها بنجاح',
    });

    setIsDeleteDialogOpen(false);
    setConversationToDelete(null);
  };

  // دالة لإجبار تحديث المحادثة بعد إرسال رسالة
  const forceUpdateConversation = (threadId: number, messageText: string) => {
    setConversations(prev => prev.map(conv => {
      if (conv.threadId === threadId) {
        return {
          ...conv,
          lastMessageText: messageText,
          lastMessageDate: new Date().toISOString(),
          lastMessageId: Date.now()
        };
      }
      return conv;
    }));
  };

  return (
    <>
      <div className="flex h-[calc(100vh-4rem)]">
        <div className="w-1/3 border-l flex flex-col">
          <div className="p-2 border-b space-y-2">
            {/* رأس المراسلات مع أدوات التحكم */}
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">المراسلات</h2>
              <div className="flex items-center gap-1">
                {/* أزرار التحكم */}
                

              </div>
            </div>



            {/* شريط البحث */}
            <div className="flex items-center gap-2">
              <Input 
                placeholder="بحث في المحادثات..." 
                className="flex-1"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Button variant="ghost" size="icon" onClick={() => setIsNewMessageDialogOpen(true)}>
                <Users className="h-5 w-5" />
              </Button>
            </div>
          </div>
          <ScrollArea className="flex-1">
            {conversations.map(conv => (
              <div key={conv.threadId} className={`p-2 border-b flex items-center group ${selectedConv?.threadId === conv.threadId ? 'bg-accent text-accent-foreground' : 'hover:bg-accent/50'}`}>
                <div className="flex-1 flex items-center cursor-pointer" onClick={()=>selectConversation(conv)}>
                  <Avatar>
                    <AvatarImage src={`https://ui-avatars.com/api/?name=${getConversationName(conv)}`} />
                    <AvatarFallback>{getConversationName(conv).charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="ml-2 flex-1 overflow-hidden">
                    <div className="flex justify-between items-center">
                      <span className="font-semibold truncate">{getConversationName(conv)}</span>
                      {conv.unreadCount > 0 && <Badge>{conv.unreadCount}</Badge>}
                    </div>
                    <p className="text-sm text-muted-foreground truncate">{conv.lastMessageText}</p>
                  </div>
                </div>
                {/* زر حذف المحادثة - يظهر فقط للمديرين */}
                {isAdmin && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 text-destructive hover:text-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteConversation(conv);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </ScrollArea>
        </div>
        <div className="flex-1 flex flex-col bg-muted/20">
          {selectedConv ? (
            <>
              <div className="p-2 border-b flex items-center bg-background shadow-sm">
                <button onClick={()=>setSelectedConv(null)} className="md:hidden p-2 mr-2">◀</button>
                <span className="ml-2 flex-1 font-semibold">{getConversationName(selectedConv)}</span>
                {selectedConv.groupName && (
                  <Button variant="ghost" size="icon" onClick={() => setIsGroupMembersDialogOpen(true)}>
                    <Users className="h-5 w-5" />
                  </Button>
                )}
              </div>
              <ScrollArea className="flex-1 p-4 space-y-4">
                {messages.map(m => {
                  const sender = users.find(u => u.id === m.senderId);
                  const isOwnMessage = m.senderId === currentUser?.id;
                  const isGroupChat = selectedConv.participants.length > 2;
                  
                  return (
                    <div key={m.id} className={`flex items-end gap-2 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                      {!isOwnMessage && (
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={`https://ui-avatars.com/api/?name=${sender?.name}`} />
                          <AvatarFallback>{(sender?.name)?.charAt(0)}</AvatarFallback>
                        </Avatar>
                      )}
                      <div className={`flex flex-col max-w-xs md:max-w-md ${isOwnMessage ? 'items-end' : 'items-start'}`}>
                        <div className={`p-3 rounded-lg ${isOwnMessage ? 'bg-primary text-primary-foreground rounded-br-none' : 'bg-background rounded-bl-none'}`}>
                          {isGroupChat && !isOwnMessage && <div className="font-bold text-xs mb-1 text-primary">{sender?.name}</div>}
                          {m.text && <p className="text-sm mb-2">{m.text}</p>}

                          {/* عرض الصور */}
                          {m.attachmentName && isImageFile(m.attachmentName) && (
                            <div className="mt-2 space-y-2">
                              <div className="relative group">
                                <img
                                  src={m.attachmentContent || m.attachmentUrl}
                                  alt={m.attachmentName}
                                  className="max-w-xs max-h-64 rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                                  onClick={() => window.open(m.attachmentUrl || m.attachmentContent, '_blank')}
                                />
                                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <div className="flex gap-1">
                                    <Button
                                      size="sm"
                                      variant="secondary"
                                      className="h-8 w-8 p-0"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        window.open(m.attachmentUrl || m.attachmentContent, '_blank');
                                      }}
                                    >
                                      <Eye className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="secondary"
                                      className="h-8 w-8 p-0"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        if (m.attachmentUrl) {
                                          downloadAttachment(m.attachmentUrl, m.attachmentName!);
                                        } else if (m.attachmentContent) {
                                          saveImageToFolder(m.attachmentContent, m.attachmentName!);
                                        }
                                      }}
                                    >
                                      <Download className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {m.attachmentName}
                                {m.attachmentSize && ` (${formatFileSize(m.attachmentSize)})`}
                              </div>
                            </div>
                          )}

                          {/* عرض ملفات PDF */}
                          {m.attachmentName && isPdfFile(m.attachmentName) && (
                            <div className="mt-2 p-3 border rounded-lg bg-muted/50">
                              <div className="flex items-center gap-2 mb-2">
                                <FileText className="h-5 w-5 text-red-600" />
                                <div className="flex-1">
                                  <div className="text-sm font-medium">{m.attachmentName}</div>
                                  {m.attachmentSize && (
                                    <div className="text-xs text-muted-foreground">
                                      {formatFileSize(m.attachmentSize)}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => window.open(m.attachmentUrl, '_blank')}
                                >
                                  <Eye className="h-4 w-4 ml-1" />
                                  فتح
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => downloadAttachment(m.attachmentUrl!, m.attachmentName!)}
                                >
                                  <Download className="h-4 w-4 ml-1" />
                                  تحميل
                                </Button>
                              </div>
                            </div>
                          )}

                          {/* عرض الملفات الأخرى */}
                          {m.attachmentName && !isImageFile(m.attachmentName) && !isPdfFile(m.attachmentName) && (
                            <div className="mt-2 p-3 border rounded-lg bg-muted/50">
                              <div className="flex items-center gap-2 mb-2">
                                <Paperclip className="h-4 w-4" />
                                <div className="flex-1">
                                  <div className="text-sm font-medium">{m.attachmentName}</div>
                                  {m.attachmentSize && (
                                    <div className="text-xs text-muted-foreground">
                                      {formatFileSize(m.attachmentSize)}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => downloadAttachment(m.attachmentUrl!, m.attachmentName!)}
                              >
                                <Download className="h-4 w-4 ml-1" />
                                تحميل
                              </Button>
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground flex items-center mt-1 px-1">
                          <span>{new Date(m.sentDate).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</span>
                          {isOwnMessage && (m.isRead ? <CheckCheck className="ml-1 h-4 w-4 text-blue-500"/> : <Check className="ml-1 h-4 w-4"/>)}
                        </div>
                      </div>
                    </div>
                  )
                })}
                <div ref={messagesEnd}/>
              </ScrollArea>
              <div className="p-4 border-t flex items-center gap-2 bg-background">
                <input
                  type="file"
                  ref={fileInput}
                  className="hidden"
                  onChange={(e) => setAttachment(e.target.files?.[0] || null)}
                />
                <Button variant="ghost" size="icon" onClick={()=>fileInput.current?.click()}><Paperclip/></Button>
                {attachment && (
                  <Badge variant="secondary" className="mr-2 flex items-center gap-1">
                    {attachment.name}
                    <span className="text-xs">({formatFileSize(attachment.size)})</span>
                    <X className="h-3 w-3 cursor-pointer" onClick={() => setAttachment(null)}/>
                  </Badge>
                )}
                <Input 
                  className="flex-1" 
                  placeholder="اكتب رسالة..." 
                  value={text} 
                  onChange={(e) => setText(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSend()}
                />
                
                {/* زر الإرسال */}
                <div className="flex gap-2">
                  <Button onClick={handleSend} disabled={!text.trim() && !attachment}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
                

              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-muted-foreground">اختر محادثة أو ابدأ جديدة</div>
          )}
        </div>

        <Dialog open={isNewMessageDialogOpen} onOpenChange={(isOpen) => {
          if (!isOpen) {
            setSelectedUsers([]);
            setSelectAll(false);
            setSearchTerm('');
            setGroupMessageText('');
            setGroupAttachment(null);
          }
          setIsNewMessageDialogOpen(isOpen);
        }}>
          <DialogContent className="max-w-lg max-h-[80vh]">
            <DialogHeader>
              <DialogTitle>بدء محادثة جديدة</DialogTitle>
            </DialogHeader>

            {/* شريط البحث وتحديد الكل */}
            <div className="space-y-2">
              <Input
                placeholder="بحث عن موظف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div className="flex items-center gap-2 p-2 border rounded-md">
                <Checkbox
                  id="select-all"
                  checked={selectAll}
                  onCheckedChange={handleSelectAll}
                />
                <label htmlFor="select-all" className="cursor-pointer flex items-center gap-2">
                  تحديد الكل ({users.filter(user => user.id !== currentUser?.id).length} موظف)
                </label>
              </div>
            </div>

            {/* قائمة الموظفين - تشمل جميع المستخدمين بما في ذلك الإدارة */}
            <ScrollArea className="h-64">
              {users
                .filter(user => user.id !== currentUser?.id)
                .filter(user =>
                  searchTerm === '' ||
                  user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  user.role?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  user.username?.toLowerCase().includes(searchTerm.toLowerCase())
                )
                .sort((a, b) => {
                  // ترتيب الإدارة أولاً
                  const aIsAdmin = a.permissions?.messaging?.viewAll || a.username === 'admin';
                  const bIsAdmin = b.permissions?.messaging?.viewAll || b.username === 'admin';
                  if (aIsAdmin && !bIsAdmin) return -1;
                  if (!aIsAdmin && bIsAdmin) return 1;
                  return a.name.localeCompare(b.name);
                })
                .map(user => {
                  const isAdminUser = user.permissions?.messaging?.viewAll || user.username === 'admin';
                  return (
                    <div key={user.id} className="p-2 flex items-center hover:bg-muted rounded-md group">
                      <Checkbox
                        id={`user-${user.id}`}
                        checked={selectedUsers.some(su => su.id === user.id)}
                        onCheckedChange={() => handleSelectUser(user)}
                        className="ml-2"
                      />
                      <label
                        htmlFor={`user-${user.id}`}
                        className="flex-grow flex items-center cursor-pointer"
                      >
                        <Avatar className="mr-3">
                          <AvatarImage src={`https://ui-avatars.com/api/?name=${user.name}`} />
                          <AvatarFallback>{(user.name)?.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-grow">
                          <div className="font-medium flex items-center gap-2">
                            {user.name}
                            {isAdminUser && (
                              <Badge variant="secondary" className="text-xs">إدارة</Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {user.role || (isAdminUser ? 'مدير النظام' : 'موظف')}
                          </div>
                        </div>
                      </label>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-xs"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDirectMessage(user);
                          }}
                        >
                          محادثة مباشرة
                        </Button>
                      </div>
                    </div>
                  );
                })}
            </ScrollArea>

            {/* قسم الرسالة الجماعية */}
            {selectedUsers.length > 0 && (
              <div className="border-t pt-4 space-y-3">
                <div className="text-sm font-medium">
                  رسالة جماعية إلى {selectedUsers.length} موظف:
                </div>
                <div className="text-xs text-muted-foreground max-h-16 overflow-y-auto">
                  {selectedUsers.map(u => u.name).join('، ')}
                </div>

                {/* خانة كتابة الرسالة المحسنة */}
                <div className="space-y-2">
                  <textarea
                    className="w-full min-h-[80px] p-3 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="اكتب رسالتك هنا..."
                    value={groupMessageText}
                    onChange={(e) => setGroupMessageText(e.target.value)}
                  />

                  {/* المرفقات */}
                  <div className="flex items-center gap-2">
                    <input
                      type="file"
                      ref={groupFileInput}
                      className="hidden"
                      onChange={(e) => setGroupAttachment(e.target.files?.[0] || null)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => groupFileInput.current?.click()}
                    >
                      <Paperclip className="h-4 w-4 ml-1" />
                      إرفاق ملف
                    </Button>
                    {groupAttachment && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        {groupAttachment.name}
                        <span className="text-xs">({formatFileSize(groupAttachment.size)})</span>
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => setGroupAttachment(null)}
                        />
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            )}

            <DialogFooter className="flex-col items-stretch gap-2 pt-2">
              {selectedUsers.length > 0 && (
                <Button
                  onClick={handleGroupSend}
                  disabled={!groupMessageText.trim() && !groupAttachment}
                  className="w-full"
                >
                  <Send className="h-4 w-4 ml-2" />
                  إرسال رسالة منفصلة لكل موظف ({selectedUsers.length})
                </Button>
              )}
              {selectedUsers.length > 1 && (
                <Button
                  onClick={handleStartConversation}
                  variant="outline"
                  className="w-full"
                >
                  <Users className="h-4 w-4 ml-2" />
                  إنشاء مجموعة وبدء المحادثة
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Dialog open={isGroupMembersDialogOpen} onOpenChange={setIsGroupMembersDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>إدارة أعضاء المجموعة</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-72">
            {users.map(user => (
              <div key={user.id} className={`p-2 flex items-center cursor-pointer hover:bg-muted rounded-md`} onClick={() => user.id !== currentUser?.id && handleSelectGroupMember(user)}>
                <Checkbox id={`group-member-${user.id}`} checked={selectedGroupMembers.some(su => su.id === user.id)} disabled={user.id === currentUser?.id} className="ml-2"/>
                <label htmlFor={`group-member-${user.id}`} className="flex-grow flex items-center cursor-pointer">
                  <Avatar className="mr-3">
                    <AvatarImage src={`https://ui-avatars.com/api/?name=${user.name}`} />
                    <AvatarFallback>{(user.name)?.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{user.name} {user.id === currentUser?.id && "(أنت)"}</div>
                    <div className="text-sm text-muted-foreground">{user.role}</div>
                  </div>
                </label>
              </div>
            ))}
          </ScrollArea>
          <DialogFooter>
            <DialogClose asChild>
                <Button variant="outline">إلغاء</Button>
            </DialogClose>
            <Button onClick={handleUpdateGroupMembers}>حفظ التغييرات</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* نافذة تأكيد حذف المحادثة */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد حذف المحادثة</AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من حذف هذه المحادثة؟ سيتم حذف جميع الرسائل نهائياً ولا يمكن التراجع عن هذا الإجراء.
              <br />
              <strong>المحادثة: {conversationToDelete ? getConversationName(conversationToDelete) : ''}</strong>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteConversation}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              حذف المحادثة
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}